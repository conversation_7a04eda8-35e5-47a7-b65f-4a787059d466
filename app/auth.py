"""
OAuth authentication module with security best practices.
"""
import secrets
import hashlib
import base64
from typing import Optional, Dict, Any
from urllib.parse import urlencode

from authlib.integrations.starlette_client import OAuth
from authlib.integrations.starlette_client.apps import StarletteOAuth2App
from fastapi import Request, HTTPException
from starlette.responses import RedirectResponse

from .config import settings


class GoogleOAuth:
    """Google OAuth handler with PKCE and security measures."""

    def __init__(self):
        self.oauth = OAuth()
        self.google: StarletteOAuth2App = self.oauth.register(
            name='google',
            client_id=settings.GOOGLE_CLIENT_ID,
            client_secret=settings.GOOGLE_CLIENT_SECRET,
            server_metadata_url='https://accounts.google.com/.well-known/openid_configuration',
            client_kwargs={
                'scope': ' '.join(settings.GOOGLE_SCOPES),
                'prompt': 'select_account',  # Force account selection
            }
        )

    def generate_state(self) -> str:
        """Generate a cryptographically secure state parameter for CSRF protection."""
        return secrets.token_urlsafe(32)

    def generate_code_verifier(self) -> str:
        """Generate PKCE code verifier."""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')

    def generate_code_challenge(self, verifier: str) -> str:
        """Generate PKCE code challenge from verifier."""
        digest = hashlib.sha256(verifier.encode('utf-8')).digest()
        return base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')

    async def get_authorization_url(self, request: Request) -> tuple[str, str, str]:
        """
        Generate authorization URL with CSRF protection and PKCE.
        Returns: (authorization_url, state, code_verifier)
        """
        # Generate CSRF state
        state = self.generate_state()

        # Generate PKCE parameters
        code_verifier = self.generate_code_verifier()
        code_challenge = self.generate_code_challenge(code_verifier)

        # Store state and code_verifier in session for verification
        request.session['oauth_state'] = state
        request.session['code_verifier'] = code_verifier

        # Generate authorization URL with PKCE
        authorization_url = await self.google.authorize_redirect(
            request,
            settings.REDIRECT_URI,
            state=state,
            code_challenge=code_challenge,
            code_challenge_method='S256'
        )

        return authorization_url.headers['location'], state, code_verifier

    async def verify_and_get_user(self, request: Request) -> Dict[str, Any]:
        """
        Verify OAuth callback and get user information.
        Includes CSRF and PKCE verification.
        """
        # Verify state parameter (CSRF protection)
        received_state = request.query_params.get('state')
        stored_state = request.session.get('oauth_state')

        if not received_state or not stored_state or received_state != stored_state:
            raise HTTPException(status_code=400, detail="Invalid state parameter")

        # Get code verifier from session
        code_verifier = request.session.get('code_verifier')
        if not code_verifier:
            raise HTTPException(status_code=400, detail="Missing code verifier")

        try:
            # Exchange authorization code for tokens with PKCE
            token = await self.google.authorize_access_token(
                request,
                code_verifier=code_verifier
            )

            # Get user information from ID token
            user_info = token.get('userinfo')
            if not user_info:
                # Fallback to userinfo endpoint if not in token
                user_info = await self.google.parse_id_token(request, token)

            # Clean up session
            request.session.pop('oauth_state', None)
            request.session.pop('code_verifier', None)

            # Validate required user information
            if not user_info or not user_info.get('email'):
                raise HTTPException(status_code=400, detail="Unable to get user information")

            return {
                'email': user_info['email'],
                'name': user_info.get('name', ''),
                'picture': user_info.get('picture', ''),
                'email_verified': user_info.get('email_verified', False),
                'sub': user_info.get('sub', '')  # Google user ID
            }

        except Exception as e:
            # Clean up session on error
            request.session.pop('oauth_state', None)
            request.session.pop('code_verifier', None)
            raise HTTPException(status_code=400, detail=f"OAuth verification failed: {str(e)}")

    def create_user_session(self, request: Request, user_info: Dict[str, Any]) -> None:
        """Create a secure user session."""
        # Store minimal user information in session
        request.session['user'] = {
            'email': user_info['email'],
            'name': user_info['name'],
            'picture': user_info['picture'],
            'authenticated': True,
            'sub': user_info['sub']
        }

    def get_current_user(self, request: Request) -> Optional[Dict[str, Any]]:
        """Get current authenticated user from session."""
        user = request.session.get('user')
        if user and user.get('authenticated'):
            return user
        return None

    def logout_user(self, request: Request) -> None:
        """Logout user by clearing session."""
        request.session.clear()


# Global OAuth instance
google_oauth = GoogleOAuth()