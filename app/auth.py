"""
OAuth authentication module with security best practices.
"""
import secrets
import hashlib
import base64
from typing import Optional, Dict, Any
from urllib.parse import urlencode

from authlib.integrations.starlette_client import OAuth
from authlib.integrations.starlette_client.apps import StarletteOAuth2App
from fastapi import Request, HTTPException
from starlette.responses import RedirectResponse

from .config import settings


class GoogleOAuth:
    """Google OAuth handler with PKCE and security measures."""

    def __init__(self):
        self.oauth = OAuth()
        self.google: StarletteOAuth2App = self.oauth.register(
            name='google',
            client_id=settings.GOOGLE_CLIENT_ID,
            client_secret=settings.GOOGLE_CLIENT_SECRET,
            authorize_url='https://accounts.google.com/o/oauth2/auth',
            access_token_url='https://oauth2.googleapis.com/token',
            userinfo_endpoint='https://openidconnect.googleapis.com/v1/userinfo',
            client_kwargs={
                'scope': ' '.join(settings.GOOGLE_SCOPES),
                'prompt': 'select_account',  # Force account selection
            }
        )

    def generate_state(self) -> str:
        """Generate a cryptographically secure state parameter for CSRF protection."""
        return secrets.token_urlsafe(32)

    def generate_code_verifier(self) -> str:
        """Generate PKCE code verifier."""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')

    def generate_code_challenge(self, verifier: str) -> str:
        """Generate PKCE code challenge from verifier."""
        digest = hashlib.sha256(verifier.encode('utf-8')).digest()
        return base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')

    async def get_authorization_url(self, request: Request) -> str:
        """
        Generate authorization URL with CSRF protection and PKCE.
        Returns: authorization_url
        """
        # Generate CSRF state
        state = self.generate_state()

        # Generate PKCE parameters
        code_verifier = self.generate_code_verifier()
        code_challenge = self.generate_code_challenge(code_verifier)

        # Store state and code_verifier in session for verification
        request.session['oauth_state'] = state
        request.session['code_verifier'] = code_verifier

        # Build authorization URL manually
        from urllib.parse import urlencode

        params = {
            'client_id': settings.GOOGLE_CLIENT_ID,
            'redirect_uri': settings.REDIRECT_URI,
            'scope': ' '.join(settings.GOOGLE_SCOPES),
            'response_type': 'code',
            'state': state,
            'code_challenge': code_challenge,
            'code_challenge_method': 'S256',
            'prompt': 'select_account'
        }

        authorization_url = f"https://accounts.google.com/o/oauth2/auth?{urlencode(params)}"
        return authorization_url

    async def verify_and_get_user(self, request: Request) -> Dict[str, Any]:
        """
        Verify OAuth callback and get user information.
        Includes CSRF and PKCE verification.
        """
        # Verify state parameter (CSRF protection)
        received_state = request.query_params.get('state')
        stored_state = request.session.get('oauth_state')

        if not received_state or not stored_state or received_state != stored_state:
            raise HTTPException(status_code=400, detail="Invalid state parameter")

        # Get code verifier from session
        code_verifier = request.session.get('code_verifier')
        if not code_verifier:
            raise HTTPException(status_code=400, detail="Missing code verifier")

        try:
            # Get authorization code from callback
            code = request.query_params.get('code')
            if not code:
                raise HTTPException(status_code=400, detail="Missing authorization code")

            # Exchange authorization code for tokens with PKCE
            import httpx
            token_data = {
                'client_id': settings.GOOGLE_CLIENT_ID,
                'client_secret': settings.GOOGLE_CLIENT_SECRET,
                'code': code,
                'grant_type': 'authorization_code',
                'redirect_uri': settings.REDIRECT_URI,
                'code_verifier': code_verifier
            }

            async with httpx.AsyncClient() as client:
                token_response = await client.post(
                    'https://oauth2.googleapis.com/token',
                    data=token_data,
                    headers={'Content-Type': 'application/x-www-form-urlencoded'}
                )

                if token_response.status_code != 200:
                    raise HTTPException(status_code=400, detail=f"Token exchange failed: {token_response.text}")

                token = token_response.json()
                access_token = token.get('access_token')

                if not access_token:
                    raise HTTPException(status_code=400, detail="No access token received")

                # Get user information using access token
                userinfo_response = await client.get(
                    'https://openidconnect.googleapis.com/v1/userinfo',
                    headers={'Authorization': f'Bearer {access_token}'}
                )

                if userinfo_response.status_code != 200:
                    raise HTTPException(status_code=400, detail="Failed to get user information")

                user_info = userinfo_response.json()

            # Clean up session
            request.session.pop('oauth_state', None)
            request.session.pop('code_verifier', None)

            # Validate required user information
            if not user_info or not user_info.get('email'):
                raise HTTPException(status_code=400, detail="Unable to get user information")

            return {
                'email': user_info['email'],
                'name': user_info.get('name', ''),
                'picture': user_info.get('picture', ''),
                'email_verified': user_info.get('email_verified', False),
                'sub': user_info.get('sub', '')  # Google user ID
            }

        except Exception as e:
            # Clean up session on error
            request.session.pop('oauth_state', None)
            request.session.pop('code_verifier', None)
            raise HTTPException(status_code=400, detail=f"OAuth verification failed: {str(e)}")

    def create_user_session(self, request: Request, user_info: Dict[str, Any]) -> None:
        """Create a secure user session."""
        # Store minimal user information in session
        request.session['user'] = {
            'email': user_info['email'],
            'name': user_info['name'],
            'picture': user_info['picture'],
            'authenticated': True,
            'sub': user_info['sub']
        }

    def get_current_user(self, request: Request) -> Optional[Dict[str, Any]]:
        """Get current authenticated user from session."""
        user = request.session.get('user')
        if user and user.get('authenticated'):
            return user
        return None

    def logout_user(self, request: Request) -> None:
        """Logout user by clearing session."""
        request.session.clear()


# Global OAuth instance
google_oauth = GoogleOAuth()