"""
FastAPI application with Google OAuth integration and security best practices.
"""
from fastapi import Fast<PERSON><PERSON>, Request, HTTPException, Depends
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.middleware.sessions import SessionMiddleware
from starlette.middleware.httpsredirect import HTTPSRedirectMiddleware
import uvicorn

from .config import settings
from .auth import google_oauth
from .security import SecurityHeadersMiddleware, RateLimitMiddleware, validate_session_security


# Create FastAPI application
app = FastAPI(
    title="Login Federation POC",
    description="A secure login federation application using Google OAuth",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None
)

# Security Middleware
if settings.HTTPS_ONLY:
    app.add_middleware(HTTPSRedirectMiddleware)

app.add_middleware(TrustedHostMiddleware, allowed_hosts=settings.ALLOWED_HOSTS)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RateLimitMiddleware, calls=100, period=60)  # 100 requests per minute

app.add_middleware(
    SessionMiddleware,
    secret_key=settings.SECRET_KEY,
    max_age=settings.SESSION_MAX_AGE,
    same_site="lax",
    https_only=settings.SECURE_COOKIES
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:8000", "http://127.0.0.1:8000"],
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)

# Static files and templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")


# Dependency to get current user
def get_current_user(request: Request):
    """Dependency to get current authenticated user with security validation."""
    # Validate session security
    if not validate_session_security(request):
        return None

    return google_oauth.get_current_user(request)


# Routes
@app.get("/", response_class=HTMLResponse)
async def login_page(request: Request, current_user=Depends(get_current_user)):
    """Main login page - redirects to home if already authenticated."""
    if current_user:
        return RedirectResponse(url="/home", status_code=302)

    return templates.TemplateResponse("login.html", {"request": request})


@app.get("/home", response_class=HTMLResponse)
async def home_page(request: Request, current_user=Depends(get_current_user)):
    """Home page - requires authentication."""
    if not current_user:
        return RedirectResponse(url="/", status_code=302)

    return templates.TemplateResponse("home.html", {
        "request": request,
        "user": current_user
    })


@app.get("/auth/login")
async def login(request: Request):
    """Initiate Google OAuth login."""
    try:
        authorization_url = await google_oauth.get_authorization_url(request)
        return RedirectResponse(url=authorization_url, status_code=302)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Login initiation failed: {str(e)}")


@app.get("/auth/callback")
async def auth_callback(request: Request):
    """Handle OAuth callback from Google."""
    try:
        # Check for error in callback
        error = request.query_params.get('error')
        if error:
            error_description = request.query_params.get('error_description', 'Unknown error')
            raise HTTPException(status_code=400, detail=f"OAuth error: {error_description}")

        # Verify and get user information
        user_info = await google_oauth.verify_and_get_user(request)

        # Create user session
        google_oauth.create_user_session(request, user_info)

        # Redirect to home page
        return RedirectResponse(url="/home", status_code=302)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Authentication failed: {str(e)}")


@app.post("/auth/logout")
async def logout(request: Request):
    """Logout user and clear session."""
    google_oauth.logout_user(request)
    return RedirectResponse(url="/", status_code=302)


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": "1.0.0"}


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        ssl_keyfile=None,
        ssl_certfile=None
    )