"""
Security middleware and utilities for enhanced application security.
"""
import time
from typing import Dict, Any
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import J<PERSON><PERSON>esponse

from .config import settings


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers to all responses."""

    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)

        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"

        # Content Security Policy
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' https://unpkg.com https://cdn.tailwindcss.com; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "img-src 'self' data: https:; "
            "connect-src 'self'; "
            "font-src 'self' https:; "
            "object-src 'none'; "
            "base-uri 'self'; "
            "form-action 'self';"
        )
        response.headers["Content-Security-Policy"] = csp

        # HSTS (only in production with HTTPS)
        if settings.HTTPS_ONLY:
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"

        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Simple rate limiting middleware."""

    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.clients: Dict[str, Dict[str, Any]] = {}

    def get_client_id(self, request: Request) -> str:
        """Get client identifier for rate limiting."""
        # Use X-Forwarded-For if behind proxy, otherwise use client IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        return request.client.host if request.client else "unknown"

    def is_rate_limited(self, client_id: str) -> bool:
        """Check if client is rate limited."""
        now = time.time()

        if client_id not in self.clients:
            self.clients[client_id] = {"count": 1, "window_start": now}
            return False

        client_data = self.clients[client_id]

        # Reset window if period has passed
        if now - client_data["window_start"] > self.period:
            client_data["count"] = 1
            client_data["window_start"] = now
            return False

        # Check if limit exceeded
        if client_data["count"] >= self.calls:
            return True

        client_data["count"] += 1
        return False

    async def dispatch(self, request: Request, call_next):
        client_id = self.get_client_id(request)

        if self.is_rate_limited(client_id):
            return JSONResponse(
                status_code=429,
                content={"detail": "Rate limit exceeded. Please try again later."},
                headers={"Retry-After": str(self.period)}
            )

        return await call_next(request)


def validate_session_security(request: Request) -> bool:
    """Validate session security requirements."""
    # Check if session exists
    if not hasattr(request, 'session'):
        return False

    # Check for session hijacking indicators
    user_agent = request.headers.get("User-Agent", "")
    stored_user_agent = request.session.get("user_agent")

    if stored_user_agent and stored_user_agent != user_agent:
        # User agent changed - possible session hijacking
        request.session.clear()
        return False

    # Store user agent on first use
    if not stored_user_agent:
        request.session["user_agent"] = user_agent

    return True


def sanitize_user_input(data: str) -> str:
    """Basic input sanitization."""
    if not isinstance(data, str):
        return str(data)

    # Remove potentially dangerous characters
    dangerous_chars = ['<', '>', '"', "'", '&', '\x00', '\r', '\n']
    for char in dangerous_chars:
        data = data.replace(char, '')

    # Limit length
    return data[:1000]


class SecurityUtils:
    """Utility class for security-related functions."""

    @staticmethod
    def is_safe_redirect_url(url: str) -> bool:
        """Check if redirect URL is safe (prevents open redirect attacks)."""
        if not url:
            return False

        # Only allow relative URLs or URLs to allowed hosts
        if url.startswith('/'):
            return True

        # Check against allowed hosts
        for host in settings.ALLOWED_HOSTS:
            if url.startswith(f'http://{host}') or url.startswith(f'https://{host}'):
                return True

        return False

    @staticmethod
    def generate_nonce() -> str:
        """Generate a cryptographically secure nonce."""
        import secrets
        return secrets.token_urlsafe(32)

    @staticmethod
    def constant_time_compare(a: str, b: str) -> bool:
        """Constant time string comparison to prevent timing attacks."""
        import hmac
        return hmac.compare_digest(a.encode(), b.encode())