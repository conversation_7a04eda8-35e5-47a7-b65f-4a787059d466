# Login Federation POC

A secure login federation application using Google OAuth 2.0 with HTMX frontend and FastAPI backend.

## Features

- **Google OAuth 2.0 Integration**: Secure authentication using Google accounts
- **PKCE Security**: Proof Key for Code Exchange for enhanced security
- **CSRF Protection**: State parameter validation to prevent cross-site request forgery
- **Secure Sessions**: Encrypted session cookies with proper security flags
- **Rate Limiting**: Protection against brute force attacks
- **Security Headers**: Comprehensive security headers including CSP, HSTS, and more
- **HTMX Frontend**: Modern, responsive frontend with smooth user experience

## Security Features

- **PKCE (Proof Key for Code Exchange)**: Prevents authorization code interception attacks
- **CSRF Protection**: State parameter validation prevents cross-site request forgery
- **Secure Session Management**: Encrypted cookies with proper security flags
- **Rate Limiting**: 100 requests per minute per IP address
- **Security Headers**: Content Security Policy, HSTS, X-Frame-Options, etc.
- **Input Validation**: Sanitization of user inputs
- **Session Security**: User agent validation to detect session hijacking

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your Google OAuth credentials
   ```

3. **Google OAuth Setup**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google+ API
   - Create OAuth 2.0 credentials
   - Add `http://localhost:8000/auth/callback` to authorized redirect URIs

4. **Run the Application**:
   ```bash
   python run.py
   ```

5. **Access the Application**:
   Open your browser and go to `http://localhost:8000`

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GOOGLE_CLIENT_ID` | Google OAuth Client ID | Required |
| `GOOGLE_CLIENT_SECRET` | Google OAuth Client Secret | Required |
| `SECRET_KEY` | Application secret key (32+ chars) | Required |
| `REDIRECT_URI` | OAuth redirect URI | `http://localhost:8000/auth/callback` |
| `ALLOWED_HOSTS` | Comma-separated allowed hosts | `localhost,127.0.0.1` |
| `SECURE_COOKIES` | Use secure cookies (HTTPS only) | `false` |
| `HTTPS_ONLY` | Enforce HTTPS | `false` |
| `DEBUG` | Enable debug mode | `true` |
| `HOST` | Server host | `0.0.0.0` |
| `PORT` | Server port | `8000` |

## Project Structure

```
login-federation-poc/
├── app/
│   ├── __init__.py
│   ├── main.py          # FastAPI application
│   ├── config.py        # Configuration management
│   ├── auth.py          # OAuth authentication
│   └── security.py      # Security middleware
├── templates/
│   ├── base.html        # Base template
│   ├── login.html       # Login page
│   └── home.html        # Home page
├── static/              # Static files (CSS, JS, images)
├── requirements.txt     # Python dependencies
├── .env                 # Environment variables
├── .env.example         # Environment template
├── run.py              # Application startup script
└── README.md           # This file
```

## API Endpoints

- `GET /` - Login page (redirects to home if authenticated)
- `GET /home` - Home page (requires authentication)
- `GET /auth/login` - Initiate Google OAuth login
- `GET /auth/callback` - OAuth callback handler
- `POST /auth/logout` - Logout and clear session
- `GET /health` - Health check endpoint

## Security Considerations

- Always use HTTPS in production
- Generate a strong, random SECRET_KEY
- Configure proper CORS settings
- Set up proper logging and monitoring
- Regularly update dependencies
- Use environment variables for sensitive data
- Implement proper error handling

## Development

For development, you can run the application with hot reload:

```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## Production Deployment

For production deployment:

1. Set `HTTPS_ONLY=true` and `SECURE_COOKIES=true`
2. Use a strong, random SECRET_KEY
3. Configure proper allowed hosts
4. Set up reverse proxy (nginx/Apache)
5. Use a production WSGI server (gunicorn)
6. Enable logging and monitoring

## License

This project is for demonstration purposes only.