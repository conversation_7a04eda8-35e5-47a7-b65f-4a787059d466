{% extends "base.html" %}

{% block title %}Login - Login Federation POC{% endblock %}

{% block content %}
<div class="flex items-center justify-center min-h-screen">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                Welcome
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                Please sign in with your Google account to continue
            </p>
        </div>

        <div class="mt-8 space-y-6">
            <div class="rounded-md shadow-sm">
                <div class="text-center">
                    <a href="/auth/login"
                       class="google-btn w-full"
                       hx-get="/auth/login"
                       hx-trigger="click"
                       hx-indicator="#login-spinner">
                        <svg class="google-icon" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Sign in with Google
                    </a>

                    <!-- Loading spinner -->
                    <div id="login-spinner" class="htmx-indicator mt-4">
                        <div class="flex justify-center">
                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                        </div>
                        <p class="text-sm text-gray-500 mt-2">Redirecting to Google...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Error handling -->
<script>
    document.body.addEventListener('htmx:responseError', function(evt) {
        console.error('Login error:', evt.detail);
        alert('Login failed. Please try again.');
    });

    document.body.addEventListener('htmx:sendError', function(evt) {
        console.error('Network error:', evt.detail);
        alert('Network error. Please check your connection and try again.');
    });
</script>
{% endblock %}