{% extends "base.html" %}

{% block title %}Home - Login Federation POC{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-900">Login Federation POC</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    {% if user.picture %}
                    <img class="h-8 w-8 rounded-full" src="{{ user.picture }}" alt="{{ user.name }}">
                    {% endif %}
                    <span class="text-sm text-gray-700">{{ user.name or user.email }}</span>
                    <form hx-post="/auth/logout" hx-trigger="submit" class="inline">
                        <button type="submit"
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <!-- Welcome Message -->
            <div class="max-w-3xl mx-auto">
                <h1 class="text-4xl font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                    <span class="block">Welcome!</span>
                </h1>
                <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
                    You have successfully logged in using Google OAuth 2.0 with PKCE security.
                </p>
            </div>

            <!-- User Information Card -->
            <div class="mt-12 max-w-lg mx-auto">
                <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-center justify-center mb-4">
                            {% if user.picture %}
                            <img class="h-20 w-20 rounded-full border-4 border-blue-200"
                                 src="{{ user.picture }}"
                                 alt="{{ user.name }}">
                            {% else %}
                            <div class="h-20 w-20 rounded-full bg-blue-500 flex items-center justify-center border-4 border-blue-200">
                                <span class="text-2xl font-bold text-white">
                                    {{ user.name[0] if user.name else user.email[0] }}
                                </span>
                            </div>
                            {% endif %}
                        </div>

                        <h3 class="text-lg font-medium text-gray-900 text-center">
                            {{ user.name or "User" }}
                        </h3>
                        <p class="text-sm text-gray-500 text-center mt-1">
                            {{ user.email }}
                        </p>

                        <div class="mt-6 border-t border-gray-200 pt-6">
                            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                                                <circle cx="4" cy="4" r="3" />
                                            </svg>
                                            Authenticated
                                        </span>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Provider</dt>
                                    <dd class="mt-1 text-sm text-gray-900 flex items-center">
                                        <svg class="h-4 w-4 mr-1" viewBox="0 0 24 24">
                                            <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                            <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                            <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                            <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                        </svg>
                                        Google
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Features -->
            <div class="mt-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Security Features Implemented</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mx-auto mb-4">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">PKCE Protection</h3>
                        <p class="text-sm text-gray-600">Proof Key for Code Exchange prevents authorization code interception attacks.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="flex items-center justify-center h-12 w-12 rounded-md bg-green-500 text-white mx-auto mb-4">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">CSRF Protection</h3>
                        <p class="text-sm text-gray-600">State parameter validation prevents cross-site request forgery attacks.</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <div class="flex items-center justify-center h-12 w-12 rounded-md bg-purple-500 text-white mx-auto mb-4">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Secure Sessions</h3>
                        <p class="text-sm text-gray-600">Encrypted session cookies with proper security flags and expiration.</p>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Logout confirmation -->
<script>
    document.body.addEventListener('htmx:beforeRequest', function(evt) {
        if (evt.detail.elt.closest('form[hx-post="/auth/logout"]')) {
            if (!confirm('Are you sure you want to logout?')) {
                evt.preventDefault();
            }
        }
    });
</script>
{% endblock %}