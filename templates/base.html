<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{% block title %}Login Federation POC{% endblock %}</title>

    <!-- Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://unpkg.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data: https:; connect-src 'self';">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="referrer" content="strict-origin-when-cross-origin">

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.6"></script>

    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <style>
        .google-btn {
            background-color: #4285f4;
            border: none;
            border-radius: 4px;
            color: white;
            font-family: 'Roboto', sans-serif;
            font-size: 14px;
            font-weight: 500;
            padding: 12px 24px;
            text-align: center;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
            cursor: pointer;
        }

        .google-btn:hover {
            background-color: #357ae8;
        }

        .google-btn:active {
            background-color: #2d5aa0;
        }

        .google-icon {
            width: 18px;
            height: 18px;
            margin-right: 8px;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4">
        {% block content %}{% endblock %}
    </div>

    <!-- HTMX Configuration -->
    <script>
        // Configure HTMX
        htmx.config.globalViewTransitions = true;
        htmx.config.defaultSwapStyle = 'innerHTML';

        // Add loading states
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            evt.target.classList.add('loading');
        });

        document.body.addEventListener('htmx:afterRequest', function(evt) {
            evt.target.classList.remove('loading');
        });

        // Add fade-in animation to new content
        document.body.addEventListener('htmx:afterSwap', function(evt) {
            evt.target.classList.add('fade-in');
        });
    </script>
</body>
</html>