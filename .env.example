# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Application Configuration
SECRET_KEY=your_secret_key_here_generate_a_strong_random_string
REDIRECT_URI=http://localhost:8000/auth/callback

# Security Configuration
ALLOWED_HOSTS=localhost,127.0.0.1
SECURE_COOKIES=false
HTTPS_ONLY=false

# Development Configuration
DEBUG=true
HOST=0.0.0.0
PORT=8000